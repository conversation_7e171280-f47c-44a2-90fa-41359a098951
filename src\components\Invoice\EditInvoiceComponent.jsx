import React, { useState, useEffect, useCallback } from "react";
import { GlobalContext, showToast } from "Src/globalContext";
import CustomSelect2 from "Components/CustomSelect2";
import moment from "moment";
import {
  ArrowLeft,
  Plus,
  Trash2,
  PlusCircle,
  Download,
  Upload,
  X,
  FileText,
} from "lucide-react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import MkdSDK from "Utils/MkdSDK";
import { SingleDatePicker } from "react-dates";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoiceQuotePDF from "./InvoiceQuotePDF";
import { uploadS3FilesAPI } from "Src/services/workOrderService";

// Status Confirmation Modal Component
const StatusConfirmationModal = ({ isOpen, onClose, onConfirm, status }) => {
  if (!isOpen) return null;

  const statusText =
    status === "3"
      ? "Paid in Full"
      : status === "2"
      ? "Deposit Paid"
      : "Unpaid";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-boxdark p-6 shadow-lg">
        <h3 className="mb-4 text-xl font-semibold text-white">
          Confirm Status Change
        </h3>
        <p className="mb-6 text-bodydark2">
          Are you sure you want to change the status to{" "}
          <strong>{statusText}</strong>?
          {status === "3" && (
            <span className="mt-2 block text-warning">
              This will mark the item as fully paid.
            </span>
          )}
          {status === "2" && (
            <span className="mt-2 block text-warning">
              This will mark the item as deposit paid.
            </span>
          )}
        </p>
        <div className="flex justify-end gap-4">
          <button
            onClick={onClose}
            className="rounded bg-boxdark-2 px-4 py-2 text-white hover:bg-opacity-90"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

const EditInvoiceComponent = ({
  id,
  onClose,
  userDetails,
  clients,
  mixTypes,
  onSubmit,
  onResend,
}) => {
  const [invoiceData, setInvoiceData] = useState({
    notes: "",
    termsAndConditions: "",
    depositAmount: 0,
    depositPercentage: 30,
    items: [],
    specialRows: [],
  });
  const [focusedInput, setFocusedInput] = React.useState({
    date: null,
    dueDate: null,
    musicSurveyDue: {},
    submission: {},
    estimatedCompletion: {},
  });
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedClientId, setSelectedClientId] = useState("");
  const [loading, setLoading] = useState(true);
  const [invoiceDates, setInvoiceDates] = useState({
    invoiceDate: new Date().toISOString().split("T")[0],
    dueDate: "",
  });
  const [totalPrice, setTotalPrice] = useState(0);
  const [isQuote, setIsQuote] = useState(false);
  const [mixSeasons, setMixSeasons] = useState([]);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [producers, setProducers] = useState([]);
  const [isMainMember, setIsMainMember] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [producerMixTypes, setProducerMixTypes] = useState({});
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newStatus, setNewStatus] = useState("");
  const [uploadedChecks, setUploadedChecks] = useState([]);
  const [isUploadingCheck, setIsUploadingCheck] = useState(false);

  // SunEditor button list
  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const getSunEditorInstance = (sunEditor) => {
    // We keep this function for the SunEditor component but don't need to store the instance
  };

  const calculateDates = (mixDate, index) => {
    if (!mixDate) return;

    // Parse settings
    const surveySettings = userDetails?.survey
      ? JSON.parse(userDetails.survey)
      : { weeks: 8, day: "Monday" };
    const routineSettings = userDetails?.routine_submission_date
      ? JSON.parse(userDetails.routine_submission_date)
      : { weeks: 1, day: "Monday" };
    const deliverySettings = userDetails?.estimated_delivery
      ? JSON.parse(userDetails.estimated_delivery)
      : { weeks: 1, day: "Friday" };

    // Function to find the previous occurrence of a day
    const findPreviousDay = (date, targetDay) => {
      const days = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const targetDayIndex = days.indexOf(targetDay);
      let currentDate = moment(date);

      while (currentDate.day() !== targetDayIndex) {
        currentDate.subtract(1, "days");
      }
      return currentDate;
    };

    // Calculate survey date (backwards from mix date)
    const surveyDate = findPreviousDay(
      moment(mixDate).subtract(surveySettings.weeks, "weeks"),
      surveySettings.day
    );

    // Calculate routine submission date (backwards from mix date)
    const submissionDate = findPreviousDay(
      moment(mixDate).subtract(routineSettings.weeks, "weeks"),
      routineSettings.day
    );

    // Calculate estimated completion date (forward from mix date)
    const completionDate = findPreviousDay(
      moment(mixDate).add(deliverySettings.weeks, "weeks"),
      deliverySettings.day
    );

    // Update invoiceData with calculated dates
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      newItems[index] = {
        ...newItems[index],
        mixDate: mixDate,
        musicSurveyDue: surveyDate.format("YYYY-MM-DD"),
        routineSubmissionDue: submissionDate.format("YYYY-MM-DD"),
        estimatedCompletion: completionDate.format("YYYY-MM-DD"),
      };
      return {
        ...prev,
        items: newItems,
      };
    });
  };

  // Fetch mix seasons for a specific producer
  const fetchMixSeasonsForProducer = useCallback(
    async (producerId) => {
      if (!producerId) return;

      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI(
          `/v4/api/records/mix_season?filter=user_id,eq,${producerId}`,
          {},
          "GET"
        );

        if (!response.error && response.list) {
          // Sort seasons in ascending order
          const sortedSeasons = response.list.sort((a, b) => {
            // First try to sort by name if it contains numbers
            const aNum = parseInt(a.name.replace(/\D/g, ""));
            const bNum = parseInt(b.name.replace(/\D/g, ""));

            if (!isNaN(aNum) && !isNaN(bNum)) {
              return aNum - bNum;
            }

            // Fall back to alphabetical sort
            return a.name.localeCompare(b.name);
          });

          // Update mixSeasons with the current user's seasons
          if (parseInt(producerId) === currentUserId) {
            setMixSeasons(sortedSeasons);
          }

          // Also store in the producerMixTypes state for access by producer ID
          setProducerMixTypes((prev) => ({
            ...prev,
            [producerId]: {
              ...prev[producerId],
              mixSeasons: sortedSeasons,
            },
          }));
        }
      } catch (error) {
        console.error(
          `Error fetching mix seasons for producer ${producerId}:`,
          error
        );
      }
    },
    [currentUserId]
  );

  // Fetch mix types for a specific producer
  const fetchMixTypesForProducer = useCallback(async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_type?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        console.log(`Mix types for producer ${producerId}:`, response.list);

        // Update the producer mix types in a consistent way with mix seasons
        setProducerMixTypes((prevState) => {
          const newState = {
            ...prevState,
            [producerId]: {
              ...prevState[producerId],
              mixTypes: response.list,
            },
          };
          console.log("Updated producerMixTypes:", newState);
          return newState;
        });
      }
    } catch (error) {
      console.error(
        `Error fetching mix types for producer ${producerId}:`,
        error
      );
    }
  }, []);

  // Fetch company info to get producer details
  useEffect(() => {
    const getInfo = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI(
          "/v3/api/custom/equality_record/user/company/info",
          {},
          "GET"
        );

        if (!response.error && response.company) {
          console.log("Company info:", response.company);
          setCompanyInfo(response.company);

          // Get current user ID from localStorage
          const userId = parseInt(localStorage.getItem("user"));
          setCurrentUserId(userId);

          // Check if current user is the main member
          const isUserMainMember =
            response.company.main_member &&
            response.company.main_member.id === userId;
          setIsMainMember(isUserMainMember);

          // Current user is now set

          // Extract producers from company info
          const producersList = [];

          // Add main member
          if (response.company.main_member) {
            producersList.push({
              id: response.company.main_member.id,
              name: `${response.company.main_member.first_name} ${response.company.main_member.last_name}`,
            });
          }

          // Add other members
          if (response.company.members && response.company.members.length > 0) {
            response.company.members.forEach((member) => {
              // Check if this member is not already in the list
              if (!producersList.some((p) => p.id === member.id)) {
                producersList.push({
                  id: member.id,
                  name: `${member.first_name} ${member.last_name}`,
                });
              }
            });
          }

          // Note: We're not adding the manager to the producers list
          // as they shouldn't be selectable as a producer

          console.log("Producers list:", producersList);
          setProducers(producersList);

          // Fetch mix seasons for the current user
          fetchMixSeasonsForProducer(userId);

          // Fetch mix types for the current user
          fetchMixTypesForProducer(userId);
        } else {
          console.error(
            "Error fetching company info:",
            response.message || "Unknown error"
          );
        }
      } catch (error) {
        console.error("Error fetching company info:", error);
      }
    };

    getInfo();
  }, [fetchMixSeasonsForProducer, fetchMixTypesForProducer]);

  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const result = await sdk.getInvoiceById(id);

        console.log("Invoice details:", result);

        setSelectedClientId(result.invoice.client_id?.toString() || "");

        // Collect all unique producer IDs from the invoice items
        const producerIds = new Set();
        result.items.forEach((item) => {
          if (item.producer) {
            producerIds.add(item.producer);
          }
        });

        // Fetch mix seasons and mix types for each producer
        const promises = [];
        producerIds.forEach((producerId) => {
          promises.push(fetchMixSeasonsForProducer(producerId));
          promises.push(fetchMixTypesForProducer(producerId));
        });

        await Promise.all(promises);

        console.log("Invoice items with status:", result.items);

        // Log mix type IDs from items
        const mixTypeIds = result.items
          .filter((item) => item.mix_type_id)
          .map((item) => ({
            id: item.id,
            mix_type_id: item.mix_type_id,
            producer: item.producer,
          }));
        console.log("Mix type IDs from items:", mixTypeIds);

        // First, format the items without trying to match mix types
        const formattedItems = result.items.map((item) => {
          // Check if this is a special row (is_special flag is set)
          const isSpecialRow = item.is_special === 1;

          if (isSpecialRow) {
            return {
              id: item.id,
              name: item.description || "Additional Charge",
              price: parseFloat(item.price) || 0,
              quantity: item.quantity || 1,
              discount: parseFloat(item.discount) || 0,
              amount: parseFloat(item.total) || 0,
              isSpecial: true,
              isSpecialRow: true,
              specialType: item.special_type || "additional",
              // Empty values for required fields
              mixDate: "",
              teamName: "",
              division: "",
              musicSurveyDue: "",
              routineSubmissionDue: "",
              estimatedCompletion: "",
              mix_season: "",
              // Ensure producer is set
              producer: "",
              producers: item.producers || [],
              // Set the status from the API response
              status: item.status ? item.status.toString() : "5",
            };
          } else {
            // Ensure producer is always a string
            const producerId = item.producer ? item.producer.toString() : "";

            // Find producer name from the producers list
            const producerName =
              producers.find((p) => p.id === parseInt(producerId))?.name || "";
            console.log(`Producer ID: ${producerId}, Name: ${producerName}`);

            return {
              id: item.id,
              mixDate: item.mix_date
                ? moment(item.mix_date).format("YYYY-MM-DD")
                : "",
              producer: producerId,
              producerName: producerName,
              mixType: item.mix_type_id ? item.mix_type_id.toString() : "",
              description: item.description || "",
              teamName: item.team_name || "",
              division: item.division || "",
              musicSurveyDue: item.music_survey_due
                ? moment(item.music_survey_due).format("YYYY-MM-DD")
                : "",
              routineSubmissionDue: item.routine_submission_due
                ? moment(item.routine_submission_due).format("YYYY-MM-DD")
                : "",
              estimatedCompletion: item.estimated_completion
                ? moment(item.estimated_completion).format("YYYY-MM-DD")
                : "",
              price: parseFloat(item.price) || 0,
              quantity: item.quantity || 1,
              discount: parseFloat(item.discount) || 0,
              // Use the mix_season_id value for proper prefilling
              mix_season: item.mix_season_id
                ? item.mix_season_id.toString()
                : "",
              amount: parseFloat(item.total) || 0,
              isSpecialRow: false,
              isSpecial: false,
              producers: item.producers || [],
              // Set the status from the API response
              status: item.status ? item.status.toString() : "5",
            };
          }
        });

        const formattedData = {
          notes: result.invoice.notes || "",
          termsAndConditions: result.invoice.terms_and_conditions || "",
          // Convert deposit_amount from cents to dollars for display
          depositAmount: parseFloat(result.invoice.deposit_amount) / 100 || 0,
          depositPercentage:
            parseFloat(result.invoice.deposit_percentage) || 30,
          items: formattedItems,
        };

        // Match mix types with descriptions for non-special items before setting the state
        const updatedItems = formattedItems.map((item) => {
          // Skip special items
          if (item.isSpecial || item.isSpecialRow) {
            return item;
          }

          // If we already have a mixType, keep it
          if (item.mixType) {
            return item;
          }

          // Try to find a matching mix type by description
          const producerId = parseInt(item.producer) || currentUserId;
          const producerMixTypesList = producerMixTypes[producerId];

          let matchingMixType = null;

          // Check if producerMixTypesList is an array (direct list of mix types)
          if (Array.isArray(producerMixTypesList)) {
            matchingMixType = producerMixTypesList.find(
              (mt) => mt.name === item.description
            );
          }
          // If it's an object with mixTypes property
          else if (
            producerMixTypesList &&
            Array.isArray(producerMixTypesList.mixTypes)
          ) {
            matchingMixType = producerMixTypesList.mixTypes.find(
              (mt) => mt.name === item.description
            );
          }

          if (matchingMixType) {
            console.log(
              `Found matching mix type for description "${item.description}":`,
              matchingMixType
            );
            return {
              ...item,
              mixType: matchingMixType.id.toString(),
            };
          }

          return item;
        });

        // Update the formatted data with the updated items
        formattedData.items = updatedItems;

        // Now set the state with the updated data
        setInvoiceData(formattedData);

        // Load existing checks if they exist
        if (result.invoice.checks) {
          try {
            const existingChecks = JSON.parse(result.invoice.checks);
            setUploadedChecks(existingChecks);
          } catch (error) {
            console.error("Error parsing existing checks:", error);
            setUploadedChecks([]);
          }
        }

        // Calculate total price
        const total = calculateTotalPrice(updatedItems);
        setTotalPrice(total);

        setInvoiceDates({
          invoiceDate: moment(result.invoice.invoice_date).format("YYYY-MM-DD"),
          dueDate: result.invoice.due_date
            ? moment(result.invoice.due_date).format("YYYY-MM-DD")
            : "",
        });
      } catch (error) {
        console.error("Error fetching invoice:", error);
        showToast(
          globalDispatch,
          "Failed to fetch invoice details",
          3000,
          "error"
        );
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchInvoiceDetails();
    }
  }, [producers]);

  const handleAddItem = () => {
    setInvoiceData((prev) => {
      // Count only non-special items to determine the next team number
      const regularItems = prev.items.filter(
        (item) => !item.isSpecial && !item.isSpecialRow
      );
      const nextTeamNumber = regularItems.length + 1;

      return {
        ...prev,
        items: [
          ...prev.items,
          {
            mixDate: "",
            producer: "",
            producerName: "",
            mixType: "",
            description: "",
            teamName: `Team ${nextTeamNumber}`,
            division: "TBD",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            price: 0,
            quantity: 1,
            discount: 0,
            mix_season: "",
            isSpecialRow: false,
            isSpecial: false,
            amount: 0,
            producers: [],
          },
        ],
      };
    });
  };

  const handleAddSpecialRow = () => {
    setInvoiceData((prev) => {
      return {
        ...prev,
        items: [
          ...prev.items,
          {
            name: "Additional Charge",
            price: 0,
            quantity: 1,
            discount: 0,
            isSpecial: true,
            isSpecialRow: true,
            specialType: "additional",
            // Empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            mix_season: "",
            // Empty producer fields
            producer: "",
            producers: [],
            amount: 0,
          },
        ],
      };
    });
  };

  const handleRemoveItem = (indexToRemove) => {
    setInvoiceData((prev) => {
      const newItems = prev.items.filter((_, index) => index !== indexToRemove);

      // Count regular items and renumber them
      let regularItemCount = 0;
      const updatedItems = newItems.map((item) => {
        if (!item.isSpecial && !item.isSpecialRow) {
          regularItemCount++;
          if (item.teamName && item.teamName.startsWith("Team ")) {
            return { ...item, teamName: `Team ${regularItemCount}` };
          }
        }
        return item;
      });

      // Recalculate total price
      const total = calculateTotalPrice(updatedItems);
      setTotalPrice(total);

      return { ...prev, items: updatedItems };
    });
  };

  const calculateTotalPrice = (items) => {
    return items.reduce((sum, item) => {
      if (item.isSpecialRow) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        const amount = Math.max(0, price - discount);
        return sum + amount;
      } else {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;
        const discountedPrice = Math.max(0, price - discount);
        const amount = discountedPrice * quantity;
        return sum + amount;
      }
    }, 0);
  };

  // Function to handle status update
  const handleStatusUpdate = async (itemId, newStatus) => {
    try {
      const sdk = new MkdSDK();

      // Get the current invoice data to update
      const currentInvoice = await sdk.getInvoiceById(id);
      if (currentInvoice.error) {
        throw new Error("Failed to fetch current invoice data");
      }

      // Find the item to update
      const itemToUpdate = currentInvoice.items.find(
        (item) => item.id === itemId
      );
      if (!itemToUpdate) {
        throw new Error("Invoice item not found");
      }

      // Update the status in the item
      itemToUpdate.status = newStatus;

      // Prepare the full invoice update payload
      const clientData = clients.find(
        (c) => c.client_id.toString() === selectedClientId
      );

      const payload = {
        clientId: parseInt(selectedClientId),
        clientEmail: clientData?.client_email || "",
        clientName: clientData?.client_program || "",
        programName: clientData?.client_program || "",
        invoiceDate: invoiceDates.invoiceDate,
        dueDate: invoiceDates.dueDate || null,
        items: currentInvoice.items.map((item) => {
          // Keep the existing item data but update the status for the specific item
          return {
            ...item,
            status: item.id === itemId ? newStatus : item.status,
          };
        }),
        depositPercentage: invoiceData.depositPercentage,
        notes: invoiceData.notes,
        termsAndConditions: invoiceData.termsAndConditions,
      };

      // Update the entire invoice
      await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${id}`,
        payload,
        "PUT"
      );

      // Show success message
      showToast(globalDispatch, "Status updated successfully", 3000, "success");

      // Close the modal
      setIsStatusModalOpen(false);
      setSelectedItem(null);
      setNewStatus("");

      // Update the local state
      const updatedItems = invoiceData.items.map((item) => {
        if (item.id === itemId) {
          return { ...item, status: newStatus };
        }
        return item;
      });

      setInvoiceData((prev) => ({
        ...prev,
        items: updatedItems,
      }));
    } catch (error) {
      console.error("Error updating status:", error);
      showToast(globalDispatch, "Failed to update status", 3000, "error");
    }
  };

  const handleItemChange = (index, field, value) => {
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      const item = newItems[index];

      if (field === "teamName") {
        if (value !== "" || !item.teamName?.startsWith("Team ")) {
          item.teamName = value;
        }
      } else if (field === "division") {
        if (value !== "" || item.division !== "TBD") {
          item.division = value;
        }
      } else if (field === "mix_season") {
        item.mix_season = value;
        // Auto-set the discount if a season is selected
        if (value) {
          // Try to find the season in the producer-specific mix seasons first
          const producerId = parseInt(item.producer);
          let selectedSeason = null;

          if (
            producerId &&
            producerMixTypes[producerId] &&
            producerMixTypes[producerId].mixSeasons
          ) {
            // Look in producer-specific mix seasons
            selectedSeason = producerMixTypes[producerId].mixSeasons.find(
              (s) => s.id === parseInt(value)
            );
          }

          // If not found, fall back to the global mix seasons
          if (!selectedSeason) {
            selectedSeason = mixSeasons.find((s) => s.id === parseInt(value));
          }

          if (selectedSeason) {
            item.discount = selectedSeason.discount || 0;
          }
        }
      } else if (field === "producer") {
        item.producer = value;

        // When producer changes, fetch their mix seasons and mix types
        const producerId = parseInt(value);
        if (producerId) {
          // Always fetch the latest data for the producer
          fetchMixTypesForProducer(producerId);
          fetchMixSeasonsForProducer(producerId);

          // Reset mix type and mix season when producer changes
          item.mixType = "";
          item.mix_season = "";
        }
      } else if (field === "status") {
        // Don't update the status directly, open the confirmation modal instead
        setSelectedItem(item);
        setNewStatus(value);
        setIsStatusModalOpen(true);
        return prev; // Return the previous state without changes
      } else {
        item[field] = value;
      }

      // Always recalculate the amount for the item regardless of which field changed
      if (item.isSpecialRow || item.isSpecial) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        item.amount = Math.max(0, price - discount);
      } else {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply fixed discount to price
        const discountedPrice = Math.max(0, price - discount);

        item.amount = discountedPrice * quantity;
      }

      // Calculate total using our improved calculateTotalPrice function
      const total = calculateTotalPrice(newItems);

      // Calculate subtotal (sum of all item amounts)
      const subtotal = newItems.reduce(
        (sum, item) => sum + (parseFloat(item.amount) || 0),
        0
      );

      // Update the total price state
      setTotalPrice(total);

      return {
        ...prev,
        items: newItems,
        subtotal,
        total,
      };
    });
  };

  // Function to handle check upload
  const handleCheckUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "application/pdf",
    ];
    if (!allowedTypes.includes(file.type)) {
      showToast(
        globalDispatch,
        "Please upload only JPG, PNG, or PDF files",
        3000,
        "error"
      );
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showToast(
        globalDispatch,
        "File size must be less than 10MB",
        3000,
        "error"
      );
      return;
    }

    try {
      setIsUploadingCheck(true);

      const formData = new FormData();
      formData.append("files", file);

      const result = await uploadS3FilesAPI(formData);

      if (!result.error && result.attachments) {
        const attachments = JSON.parse(result.attachments);
        const uploadedFile = attachments[0];

        const newCheck = {
          id: Date.now(), // Simple ID for frontend
          url: uploadedFile.url,
          filename: uploadedFile.filename,
          uploadDate: moment().format("YYYY-MM-DD HH:mm:ss"),
        };

        setUploadedChecks((prev) => [...prev, newCheck]);
        showToast(
          globalDispatch,
          "Check uploaded successfully",
          3000,
          "success"
        );
      } else {
        throw new Error(result.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading check:", error);
      showToast(globalDispatch, "Failed to upload check", 3000, "error");
    } finally {
      setIsUploadingCheck(false);
      // Reset the input
      event.target.value = "";
    }
  };

  // Function to delete a check
  const handleDeleteCheck = (checkId) => {
    setUploadedChecks((prev) => prev.filter((check) => check.id !== checkId));
    showToast(globalDispatch, "Check removed", 3000, "success");
  };

  // Function to handle quote download
  const handleQuoteDownload = () => {
    if (!selectedClientId) {
      showToast(globalDispatch, "Please select a client", 3000, "error");
      return false;
    }
    return true;
  };

  const handleUpdateInvoice = async () => {
    if (!selectedClientId) {
      showToast(globalDispatch, "Please select a client", 3000, "error");
      return;
    }

    // Format items with IDs for the API
    const formattedItems = invoiceData.items.map((item) => {
      if (item.isSpecialRow || item.isSpecial) {
        return {
          id: item.id, // Include ID for existing items
          price: parseFloat(item.price) || 0,
          quantity: parseInt(item.quantity) || 1,
          discount: parseFloat(item.discount) || 0,
          producer: item.producer || "",
          producers: item.producers || [],
          specialType: item.specialType || "additional",
          isSpecial: true,
          name: item.name || "Additional Charge",
          description: item.name || "Additional Charge", // Add description for special charges
          mixDate: "",
          teamName: "",
          division: "",
          musicSurveyDue: "",
          routineSubmissionDue: "",
          estimatedCompletion: "",
          mixSeasonId: item.mix_season || "",
          mixTypeId: "",
        };
      } else {
        // Find the mix type name from the producer's mix types
        let mixTypeName = "";
        const producerId = parseInt(item.producer) || currentUserId;
        const producerMixTypesList = producerMixTypes[producerId];

        let selectedMixType = null;

        // Check if producerMixTypesList is an array (direct list of mix types)
        if (Array.isArray(producerMixTypesList)) {
          selectedMixType = producerMixTypesList.find(
            (mt) => mt.id === parseInt(item.mixType)
          );
        }
        // If it's an object with mixTypes property
        else if (
          producerMixTypesList &&
          Array.isArray(producerMixTypesList.mixTypes)
        ) {
          selectedMixType = producerMixTypesList.mixTypes.find(
            (mt) => mt.id === parseInt(item.mixType)
          );
        }

        if (selectedMixType) {
          mixTypeName = selectedMixType.name;
          console.log(
            `Found mix type name: ${mixTypeName} for mix type ID: ${item.mixType}`
          );
        } else {
          // If we can't find the mix type in the producer's list, use the description field
          // which should already contain the mix type name
          mixTypeName = item.description || "";
          console.log(
            `Using description as mix type name: ${mixTypeName} for mix type ID: ${item.mixType}`
          );
        }

        return {
          id: item.id, // Include ID for existing items
          price: parseFloat(item.price) || 0,
          quantity: parseInt(item.quantity) || 1,
          discount: parseFloat(item.discount) || 0,
          producer: item.producer || "",
          producers: item.producers || [],
          specialType: "",
          isSpecial: false,
          description: `${mixTypeName}`, // Format description as "Mix Type - [name]"
          mixDate: item.mixDate || "",
          teamName: item.teamName || "",
          division: item.division || "",
          musicSurveyDue: item.musicSurveyDue || "",
          routineSubmissionDue: item.routineSubmissionDue || "",
          estimatedCompletion: item.estimatedCompletion || "",
          mixSeasonId: item.mix_season || "",
          mixTypeId: item.mixType || "",
        };
      }
    });

    // Convert deposit amount to cents if using fixed amount
    const depositAmount =
      invoiceData.depositPercentage > 0
        ? 0
        : Math.round(invoiceData.depositAmount * 100); // Convert to cents

    // Create the final payload
    const payload = {
      selectedClientId,
      invoiceDates,
      invoiceData: {
        ...invoiceData,
        items: formattedItems,
        depositAmount: depositAmount,
        checks: JSON.stringify(uploadedChecks), // Add checks as stringified array
      },
      isQuote,
    };

    console.log("Update invoice payload:", payload);
    onSubmit(payload);
  };

  return (
    <div className="rounded-lg bg-boxdark p-6">
      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.manager?.license_company_logo &&
          companyInfo.manager.license_company_logo !== "null" ? (
            <img
              src={companyInfo.manager.license_company_logo}
              alt={companyInfo.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.main_member?.license_company_logo &&
            companyInfo.main_member.license_company_logo !== "null" ? (
            <img
              src={companyInfo.main_member.license_company_logo}
              alt={companyInfo.main_member.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.company_logo &&
            userDetails.company_logo !== "null" ? (
            <img
              src={userDetails.company_logo}
              alt={userDetails?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.manager?.company_name &&
              companyInfo.manager.company_name !== "null"
                ? companyInfo.manager.company_name
                : companyInfo?.main_member?.company_name &&
                  companyInfo.main_member.company_name !== "null"
                ? companyInfo.main_member.company_name
                : userDetails?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {companyInfo?.manager?.office_email &&
              companyInfo.manager.office_email !== "null"
                ? companyInfo.manager.office_email
                : companyInfo?.main_member?.office_email &&
                  companyInfo.main_member.office_email !== "null"
                ? companyInfo.main_member.office_email
                : userDetails?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">INVOICE</h2>
        </div>
      </div>

      {/* Header with back button */}
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onClose}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
        <h2 className="text-2xl font-bold text-white">Edit Invoice #{id}</h2>
        <button
          onClick={onResend}
          disabled={loading}
          className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 font-medium text-white hover:bg-opacity-90"
        >
          <FontAwesomeIcon icon="paper-plane" className="h-4 w-4" />
          Resend Link
        </button>
      </div>

      {/* Client Information */}
      <div className="mb-3 w-[307px] pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="flex items-center gap-4">
          <div className="w-2/3">
            <select
              value={selectedClientId || ""}
              onChange={(e) => setSelectedClientId(e.target.value)}
              className="h-10 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-3 py-2 text-sm text-white"
              disabled
            >
              <option value="">Select Client</option>
              {clients.map((client) => (
                <option key={client.client_id} value={client.client_id}>
                  {client.client_program}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Invoice Dates Section */}
      <div className="mb-3 flex items-center justify-start pb-3">
        <div className="flex items-center justify-normal gap-3">
          <div>
            <label className="mb-1.5 block font-medium text-white">
              Invoice Date
            </label>
            <input
              type="date"
              value={invoiceDates.invoiceDate}
              onChange={(e) =>
                setInvoiceDates((prev) => ({
                  ...prev,
                  invoiceDate: e.target.value,
                }))
              }
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-5 py-1.5 text-[12px] text-white"
            />
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-3">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due / Submission Due / Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount ($)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Season
                </th>
                {(isMainMember || companyInfo?.company?.manager) && (
                  <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                    Status
                  </th>
                )}
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoiceData.items.map((item, index) =>
                !item.isSpecialRow ? (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                  >
                    <td className="row-date2 whitespace-nowrap px-4 py-3">
                      <SingleDatePicker
                        id={`mixDate_${index}`}
                        date={item.mixDate ? moment(item.mixDate) : null}
                        onDateChange={(date) => {
                          handleItemChange(
                            index,
                            "mixDate",
                            date ? date.format("YYYY-MM-DD") : null
                          );
                          calculateDates(
                            date ? date.format("YYYY-MM-DD") : null,
                            index
                          );
                        }}
                        focused={focusedInput[`mixDate_${index}`]}
                        onFocusChange={({ focused }) =>
                          setFocusedInput((prev) => ({
                            ...prev,
                            [`mixDate_${index}`]: focused,
                          }))
                        }
                        numberOfMonths={1}
                        isOutsideRange={() => false}
                        displayFormat="MM-DD-YYYY"
                        placeholder="Select Mix Date"
                        readOnly={true}
                        customInputIcon={null}
                        noBorder={true}
                        block
                        className="w-full rounded border border-stroke/50"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {isMainMember ? (
                        <CustomSelect2
                          position="top"
                          label="Select Producer"
                          textClass="!text-[10px]"
                          value={item.producer || ""}
                          onChange={(value) => {
                            const producerId = value;
                            const producerName =
                              producers.find(
                                (p) => p.id === parseInt(producerId)
                              )?.name || "";
                            handleItemChange(index, "producer", producerId);
                            handleItemChange(
                              index,
                              "producerName",
                              producerName
                            );

                            // Fetch mix seasons and mix types for the selected producer
                            if (producerId) {
                              console.log(
                                `Fetching mix data for producer: ${producerId}`
                              );
                              fetchMixSeasonsForProducer(parseInt(producerId));
                              fetchMixTypesForProducer(parseInt(producerId));

                              // Reset mix type and mix season when producer changes
                              handleItemChange(index, "mixType", "");
                              handleItemChange(index, "mix_season", "");
                              handleItemChange(index, "description", "");
                            }
                          }}
                          className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[10px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                          options={producers.map((producer) => ({
                            value: producer.id.toString(),
                            label: producer.name,
                          }))}
                        />
                      ) : (
                        <input
                          type="text"
                          value={item.producerName || "Unknown Producer"}
                          disabled={true}
                          className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        />
                      )}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[10px]"
                        value={item.mixType}
                        onChange={(value) => {
                          console.log(
                            `Changing mix type to: ${value} for item at index ${index}`
                          );
                          handleItemChange(index, "mixType", value);

                          // Get the mix types for this item's producer
                          const itemProducerId =
                            parseInt(item.producer) || currentUserId;
                          const producerMixTypesList =
                            producerMixTypes[itemProducerId];

                          console.log(
                            `Producer ID: ${itemProducerId}, Mix Types List:`,
                            producerMixTypesList
                          );

                          let selectedMixType = null;

                          // Check if producerMixTypesList is an array (direct list of mix types)
                          if (Array.isArray(producerMixTypesList)) {
                            selectedMixType = producerMixTypesList.find(
                              (mt) => mt.id === parseInt(value)
                            );
                          }
                          // If it's an object with mixTypes property
                          else if (
                            producerMixTypesList &&
                            Array.isArray(producerMixTypesList.mixTypes)
                          ) {
                            selectedMixType =
                              producerMixTypesList.mixTypes.find(
                                (mt) => mt.id === parseInt(value)
                              );
                          }

                          console.log("Selected mix type:", selectedMixType);

                          if (selectedMixType) {
                            // Update price if available
                            if (selectedMixType.price) {
                              handleItemChange(
                                index,
                                "price",
                                parseFloat(selectedMixType.price) || 0
                              );
                            }

                            // Update description with mix type name only (not "Mix Type - ")
                            const mixTypeName = selectedMixType.name;
                            if (mixTypeName) {
                              console.log(
                                `Setting description to: ${mixTypeName}`
                              );
                              handleItemChange(
                                index,
                                "description",
                                mixTypeName
                              );
                            }
                          }
                        }}
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[10px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                      >
                        <option value="">Select Mix Type</option>
                        {(() => {
                          const producerId =
                            parseInt(item.producer) || currentUserId;
                          const mixTypesList = producerMixTypes[producerId];

                          console.log(
                            `Rendering mix types for producer ${producerId}:`,
                            mixTypesList
                          );
                          console.log(
                            `Current item mix type value: ${item.mixType}`
                          );

                          // Check if mixTypesList is an array (direct list of mix types)
                          if (Array.isArray(mixTypesList)) {
                            return mixTypesList.map((mixType) => (
                              <option key={mixType.id} value={mixType.id}>
                                {mixType.name} - ${mixType.price}
                              </option>
                            ));
                          }
                          // If it's an object with mix types (from the structure with mixSeasons)
                          else if (
                            mixTypesList &&
                            Array.isArray(mixTypesList.mixTypes)
                          ) {
                            return mixTypesList.mixTypes.map((mixType) => (
                              <option key={mixType.id} value={mixType.id}>
                                {mixType.name} - ${mixType.price}
                              </option>
                            ));
                          }
                          // Fallback to empty array if no mix types found
                          return [];
                        })()}
                      </CustomSelect2>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.teamName}
                        onChange={(e) =>
                          handleItemChange(index, "teamName", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value.startsWith("Team ")) {
                            handleItemChange(index, "teamName", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.division}
                        onChange={(e) =>
                          handleItemChange(index, "division", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value === "TBD") {
                            handleItemChange(index, "division", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex flex-col gap-2">
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`musicSurveyDue_${index}`}
                            date={
                              item.musicSurveyDue
                                ? moment(item.musicSurveyDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "musicSurveyDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={focusedInput[`musicSurveyDue_${index}`]}
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`musicSurveyDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`routineSubmissionDue_${index}`}
                            date={
                              item.routineSubmissionDue
                                ? moment(item.routineSubmissionDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "routineSubmissionDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`routineSubmissionDue_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`routineSubmissionDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`estimatedCompletion_${index}`}
                            date={
                              item.estimatedCompletion
                                ? moment(item.estimatedCompletion)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "estimatedCompletion",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`estimatedCompletion_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`estimatedCompletion_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Completion Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                      </div>
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <CustomSelect2
                          value={item.mix_season}
                          disabled={item.mix_season}
                          onChange={(value) => {
                            // First set the mix season
                            const newItems = [...invoiceData.items];
                            newItems[index].mix_season = value;

                            // Auto-set the discount if a season is selected
                            if (value) {
                              const selectedSeason = mixSeasons.find(
                                (s) => s.id === parseInt(value)
                              );
                              if (selectedSeason && selectedSeason.discount) {
                                newItems[index].discount =
                                  parseFloat(selectedSeason.discount) || 0;

                                // Recalculate the amount for this item
                                const price =
                                  parseFloat(newItems[index].price) || 0;
                                const quantity =
                                  parseInt(newItems[index].quantity) || 1;
                                const discount =
                                  parseFloat(newItems[index].discount) || 0;

                                // Apply fixed discount to price
                                const discountedPrice = Math.max(
                                  0,
                                  price - discount
                                );
                                newItems[index].amount =
                                  discountedPrice * quantity;
                              }
                            }

                            // Update the state directly to avoid multiple renders
                            setInvoiceData((prev) => ({
                              ...prev,
                              items: newItems,
                            }));

                            // Recalculate total
                            const total = calculateTotalPrice(newItems);
                            setTotalPrice(total);
                          }}
                          className="w-full rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        >
                          <option value="">Select Season</option>
                          {mixSeasons.map((season) => (
                            <option key={season.id} value={season.id}>
                              {season.name}
                            </option>
                          ))}
                        </CustomSelect2>
                      </div>
                    </td>
                    {(isMainMember || companyInfo?.company?.manager) && (
                      <td className="whitespace-nowrap px-4 py-3">
                        <CustomSelect2
                          position="top"
                          label="Select Producer"
                          textClass="!text-[10px]"
                          value={item.status || "5"}
                          onChange={(value) =>
                            handleItemChange(index, "status", value)
                          }
                          className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                          options={[
                            { value: "3", label: "Paid in Full" },
                            { value: "2", label: "Deposit Paid" },
                            { value: "5", label: "Unpaid" },
                          ]}
                        />
                      </td>
                    )}
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ) : (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 bg-meta-4/20 text-[12px] hover:bg-primary/5"
                  >
                    <td colSpan={2} className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.name}
                        onChange={(e) =>
                          handleItemChange(index, "name", e.target.value)
                        }
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        placeholder="Charge Name"
                      />
                    </td>
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for spacing */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      {/* Empty cell for dates */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        max="100"
                        placeholder="0%"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for mix season column */}
                    </td>
                    {(isMainMember || companyInfo?.company?.manager) && (
                      <td className="whitespace-nowrap px-4 py-3">
                        <CustomSelect2
                          position="top"
                          label="Select Status"
                          textClass="!text-[10px]"
                          className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                          value={item.status || "5"}
                          onChange={(value) =>
                            handleItemChange(index, "status", value)
                          }
                          options={[
                            { value: "3", label: "Paid in Full" },
                            { value: "2", label: "Deposit Paid" },
                            { value: "5", label: "Unpaid" },
                          ]}
                        />
                      </td>
                    )}
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-2 flex justify-end gap-2">
          <button
            onClick={handleAddSpecialRow}
            className="flex items-center gap-2 rounded bg-meta-5 px-4 py-1 text-sm font-medium text-white hover:bg-meta-5/80"
          >
            <PlusCircle className="h-5 w-5" />
            Add Special Charge
          </button>
          <button
            onClick={handleAddItem}
            className="flex items-center gap-2 rounded bg-primary px-4 py-1 text-sm font-medium text-white hover:bg-primary/80"
          >
            <Plus className="h-5 w-5" />
            Add Row
          </button>
        </div>
      </div>

      {/* Invoice Total Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <div className="flex justify-end">
          <div className="w-1/3">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                Invoice total:
              </span>
              <span className="text-lg font-bold text-white">
                ${totalPrice.toFixed(2)}
              </span>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">Deposit:</span>
              <span className="text-sm text-white">
                $
                {invoiceData.depositPercentage > 0
                  ? (
                      (totalPrice * invoiceData.depositPercentage) /
                      100
                    ).toFixed(2)
                  : invoiceData.depositAmount.toFixed(2)}
              </span>
            </div>
            <div className="flex items-center justify-between border-t border-stroke/50 pt-2">
              <span className="text-sm font-medium text-white">
                Balance due:
              </span>
              <span className="text-sm font-bold text-white">
                $
                {invoiceData.depositPercentage > 0
                  ? (
                      totalPrice -
                      (totalPrice * invoiceData.depositPercentage) / 100
                    ).toFixed(2)
                  : (totalPrice - invoiceData.depositAmount).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Deposit and Terms Section */}
      <div className="mt-6 grid grid-cols-3 gap-6">
        {/* Left Column - Deposit Settings */}
        <div className="space-y-2">
          <h3 className="text-base font-semibold text-white">
            Deposit Settings
          </h3>
          <div className="space-y-2">
            <div>
              <label className="mb-1.5 block text-sm text-white">Type</label>
              <select
                value={
                  invoiceData.depositPercentage > 0 ? "percentage" : "amount"
                }
                onChange={(e) => {
                  const isAmount = e.target.value === "amount";
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isAmount ? 0 : 0,
                    depositPercentage: isAmount
                      ? 0
                      : prev.depositPercentage ||
                        userDetails?.deposit_percent ||
                        30,
                  }));
                }}
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
              >
                <option value="amount">Fixed Amount</option>
                <option value="percentage">Percentage</option>
              </select>
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                {invoiceData.depositPercentage > 0
                  ? "Percentage (%)"
                  : "Amount ($)"}
              </label>
              <input
                type="number"
                value={
                  invoiceData.depositPercentage > 0
                    ? invoiceData.depositPercentage
                    : invoiceData.depositAmount
                }
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  const isPercentage = invoiceData.depositPercentage > 0;
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isPercentage ? 0 : value,
                    depositPercentage: isPercentage ? value : 0,
                  }));
                }}
                placeholder={
                  invoiceData.depositPercentage > 0
                    ? `Default: ${userDetails?.deposit_percent || 30}%`
                    : "Enter amount"
                }
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
                min="0"
                max={invoiceData.depositPercentage > 0 ? 100 : undefined}
              />
            </div>
          </div>
        </div>

        {/* Middle and Right Columns - Notes and Terms */}
        <div className="col-span-2 space-y-2">
          <h3 className="text-base font-semibold text-white">Notes & Terms</h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="mb-1.5 block text-sm text-white">Notes</label>
              <textarea
                value={invoiceData.notes}
                onChange={(e) =>
                  setInvoiceData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="h-[110px] w-full rounded border border-stroke bg-transparent px-3 py-2 text-sm text-white"
                rows="4"
                placeholder="Add any additional notes..."
              />
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                Terms and Conditions
              </label>
              <SunEditor
                setContents={invoiceData.termsAndConditions}
                onChange={(content) =>
                  setInvoiceData((prev) => ({
                    ...prev,
                    termsAndConditions: content,
                  }))
                }
                getSunEditorInstance={getSunEditorInstance}
                setOptions={{
                  buttonList: buttonList.complex,
                  height: 110,
                  width: "100%",
                  placeholder: userDetails?.contract_agreement
                    ? "Terms and conditions loaded from your settings"
                    : "Add terms and conditions...",
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Check Upload Section */}
      <div className="mt-6 rounded-lg bg-meta-4/20 p-4">
        <h3 className="mb-4 text-base font-semibold text-white">
          Check Uploads
        </h3>

        {/* Upload Area */}
        <div className="mb-4">
          <label className="mb-2 block text-sm text-white">
            Upload Check Images/PDFs
          </label>
          <div className="flex items-center gap-4">
            <input
              type="file"
              accept=".jpg,.jpeg,.png,.pdf"
              onChange={handleCheckUpload}
              className="hidden"
              id="check-upload"
              disabled={isUploadingCheck}
            />
            <label
              htmlFor="check-upload"
              className={`flex cursor-pointer items-center gap-2 rounded bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-opacity-90 ${
                isUploadingCheck ? "cursor-not-allowed opacity-50" : ""
              }`}
            >
              <Upload className="h-4 w-4" />
              {isUploadingCheck ? "Uploading..." : "Upload Check"}
            </label>
            <span className="text-xs text-bodydark2">
              Supported: JPG, PNG, PDF (Max 10MB)
            </span>
          </div>
        </div>

        {/* Uploaded Checks List */}
        {uploadedChecks.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white">Uploaded Checks:</h4>
            <div className="space-y-2">
              {uploadedChecks.map((check) => (
                <div
                  key={check.id}
                  className="flex items-center justify-between rounded border border-stroke/50 bg-boxdark-2/40 p-3"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm font-medium text-white">
                        {check.filename}
                      </p>
                      <p className="text-xs text-bodydark2">
                        Uploaded:{" "}
                        {moment(check.uploadDate).format("MMM DD, YYYY HH:mm")}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <a
                      href={check.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="rounded bg-meta-5 px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                    >
                      View
                    </a>
                    <button
                      onClick={() => handleDeleteCheck(check.id)}
                      className="rounded bg-danger px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end gap-4">
        <button
          onClick={onClose}
          className="rounded-lg border border-stroke bg-opacity-80 px-6 py-2 text-bodydark"
        >
          Cancel
        </button>

        {/* Add Save as Quote button */}
        <PDFDownloadLink
          document={
            <InvoiceQuotePDF
              data={{
                selectedClientId,
                newClientData: {},
                invoiceDates,
                invoiceData: { ...invoiceData, isQuote: true },
                clientDetails:
                  clients.find(
                    (client) => client.client_id === parseInt(selectedClientId)
                  ) || {},
              }}
              userDetails={userDetails}
              companyInfo={companyInfo}
            />
          }
          fileName={`quote-${moment().format("YYYY-MM-DD")}.pdf`}
          className={`flex items-center gap-2 rounded-lg bg-meta-5 px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 ${
            loading ? "cursor-not-allowed opacity-50" : ""
          }`}
          onClick={handleQuoteDownload}
        >
          {({ loading: pdfLoading }) => (
            <>
              <Download className="h-4 w-4" />
              {pdfLoading ? "Generating Quote..." : "Save as Quote"}
            </>
          )}
        </PDFDownloadLink>

        <button
          onClick={() => {
            setIsQuote(false);
            handleUpdateInvoice();
          }}
          disabled={loading}
          className="rounded-lg bg-primary px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50"
        >
          {loading ? "Updating..." : "Update Invoice"}
        </button>
      </div>

      {/* Status Confirmation Modal */}
      <StatusConfirmationModal
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        status={newStatus}
        onConfirm={() => {
          if (selectedItem && selectedItem.id && newStatus) {
            handleStatusUpdate(selectedItem.id, newStatus);
          }
        }}
      />
    </div>
  );
};

export default EditInvoiceComponent;
